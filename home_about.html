<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Hill Country Sanctuary | Sri Lanka Hospitality</title>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500&display=swap" rel="stylesheet">
  
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: linear-gradient(135deg, #0f2a0f 0%, #031a03 100%);
      color: #053f05;
      overflow-x: hidden;
    }

    .hero-section {
      display: flex;
      width: 100%;
      min-height: 100vh;
      align-items: center;
      justify-content: space-between;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 400px;
      height: 400px;
      background: radial-gradient(circle, rgba(50, 205, 50, 0.15) 0%, transparent 70%);
      border-radius: 50%;
      z-index: 0;
      animation: pulse 8s infinite;
    }

    .hero-section::after {
      content: '';
      position: absolute;
      bottom: -200px;
      left: -200px;
      width: 500px;
      height: 500px;
      background: radial-gradient(circle, rgba(144, 238, 144, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      z-index: 0;
      animation: pulse 12s infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 0.8; }
      50% { transform: scale(1.05); opacity: 1; }
    }

    .image-slider {
      width: 55%;
      height: 100vh;
      position: relative;
      border-radius: 0 40px 40px 0;
      overflow: hidden;
      box-shadow: 0 40px 80px rgba(0, 0, 0, 0.3);
      z-index: 1;
    }

    .image-slider::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(56, 142, 60, 0.15) 0%, transparent 50%);
      z-index: 2;
      pointer-events: none;
    }

    /* Enhanced Agricultural Slider Controls */
    .slider-controls {
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      gap: 12px;
      z-index: 10;
      background: rgba(0, 0, 0, 0.4);
      backdrop-filter: blur(15px);
      padding: 20px 30px;
      border-radius: 50px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
    }

    .slider-progress {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .progress-segment {
      width: 45px;
      height: 6px;
      background: rgba(255, 255, 255, 0.25);
      border-radius: 3px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.5s ease;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .progress-segment::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 0%;
      background: linear-gradient(90deg, #66bb6a, #a5d6a7);
      border-radius: 3px;
      transition: width 0.4s ease;
    }

    .progress-segment.active::before {
      width: 100%;
      animation: progressFill 5s linear;
    }

    .progress-segment:hover {
      background: rgba(255, 255, 255, 0.6);
      transform: scaleY(1.7);
    }

    @keyframes progressFill {
      from { width: 0%; }
      to { width: 100%; }
    }

    /* Enhanced Seed Growth Slider */
    .seed-controls {
      position: absolute;
      bottom: 30px;
      right: 30px;
      display: flex;
      flex-direction: column;
      gap: 18px;
      z-index: 10;
    }

    .seed-dot {
      width: 20px;
      height: 20px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      cursor: pointer;
      position: relative;
      transition: all 0.6s ease;
      border: 2px solid rgba(255, 255, 255, 0.5);
      box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
    }

    .seed-dot::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 8px;
      height: 8px;
      background: #66bb6a;
      border-radius: 50%;
      transform: translate(-50%, -50%) scale(0);
      transition: all 0.4s ease;
    }

    .seed-dot::after {
      content: '';
      position: absolute;
      top: -10px;
      left: 50%;
      width: 2px;
      height: 0px;
      background: #66bb6a;
      transform: translateX(-50%);
      transition: all 0.6s ease;
    }

    .seed-dot.active::before {
      transform: translate(-50%, -50%) scale(1);
    }

    .seed-dot.active::after {
      height: 25px;
      top: -30px;
    }

    .seed-dot.active {
      background: rgba(102, 187, 106, 0.4);
      border-color: #66bb6a;
      box-shadow: 0 0 20px rgba(102, 187, 106, 0.7);
    }

    .seed-dot:hover {
      transform: scale(1.4);
      background: rgba(255, 255, 255, 0.8);
    }

    /* Enhanced Leaf Navigation */
    .leaf-nav {
      position: absolute;
      bottom: 30px;
      left: 30px;
      display: flex;
      gap: 12px;
      z-index: 10;
    }

    .leaf-control {
      width: 35px;
      height: 35px;
      background: linear-gradient(135deg, #66bb6a, #a5d6a7);
      border-radius: 0 100% 0 100%;
      cursor: pointer;
      position: relative;
      transition: all 0.5s ease;
      opacity: 0.7;
      transform: rotate(45deg);
      box-shadow: 0 8px 15px rgba(102, 187, 106, 0.3);
    }

    .leaf-control::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 2px;
      height: 18px;
      background: #388e3c;
      transform: translate(-50%, -50%);
    }

    .leaf-control.active {
      opacity: 1;
      transform: rotate(45deg) scale(1.3);
      box-shadow: 0 0 25px rgba(102, 187, 106, 0.8);
    }

    .leaf-control:hover {
      transform: rotate(45deg) scale(1.2);
      opacity: 0.95;
    }

    /* Enhanced Image Transitions */
    .image-slider img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition: all 2s ease-in-out;
      transform: scale(1.07) rotate(0.7deg);
      filter: brightness(0.85) contrast(1.15) saturate(1.1);
      loading: lazy;
    }

    .image-slider img.active {
      opacity: 1;
      z-index: 1;
      transform: scale(1) rotate(0deg);
      filter: brightness(1) contrast(1) saturate(1);
    }

    /* Enhanced Floating Elements Animation */
    .floating-elements {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 3;
    }

    .floating-leaf {
      position: absolute;
      width: 25px;
      height: 25px;
      background: linear-gradient(135deg, #66bb6a, #a5d6a7);
      border-radius: 0 100% 0 100%;
      opacity: 0.4;
      animation: floatLeaf 18s infinite linear;
      box-shadow: 0 4px 10px rgba(102, 187, 106, 0.3);
    }

    .floating-leaf:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
    .floating-leaf:nth-child(2) { top: 30%; left: 80%; animation-delay: 4s; }
    .floating-leaf:nth-child(3) { top: 60%; left: 20%; animation-delay: 8s; }
    .floating-leaf:nth-child(4) { top: 80%; left: 70%; animation-delay: 12s; }

    @keyframes floatLeaf {
      0% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0;
      }
      10% {
        opacity: 0.4;
      }
      50% {
        transform: translateY(-25px) rotate(10deg) scale(1.1);
        opacity: 0.6;
      }
      90% {
        opacity: 0.4;
      }
      100% {
        transform: translateY(-0px) rotate(0deg) scale(1);
        opacity: 0;
      }
    }

    /* Enhanced Slider Loading Animation */
    .slider-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 5;
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .slider-loading.show {
      opacity: 1;
    }

    .loading-sprout {
      width: 45px;
      height: 45px;
      position: relative;
    }

    .loading-sprout::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 5px;
      height: 25px;
      background: #66bb6a;
      transform: translateX(-50%);
      animation: growStem 1.2s ease-in-out infinite;
    }

    .loading-sprout::after {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      width: 20px;
      height: 20px;
      background: linear-gradient(135deg, #66bb6a, #a5d6a7);
      border-radius: 0 100% 0 100%;
      transform: translateX(-50%);
      animation: growLeaf 1.2s ease-in-out infinite 0.6s;
    }

    @keyframes growStem {
      0%, 100% { height: 25px; }
      50% { height: 35px; }
    }

    @keyframes growLeaf {
      0%, 100% { transform: translateX(-50%) scale(1); }
      50% { transform: translateX(-50%) scale(1.3); }
    }

    /* Enhanced Growth Timeline Slider */
    .growth-timeline {
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      gap: 30px;
      z-index: 15;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(25px);
      padding: 30px 50px;
      border-radius: 80px;
      border: 2px solid rgba(102, 187, 106, 0.4);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
    }

    .growth-stage {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.5s ease;
      position: relative;
      z-index: 1;
    }

    .growth-icon {
      width: 55px;
      height: 55px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26px;
      margin-bottom: 12px;
      transition: all 0.5s ease;
      border: 3px solid rgba(255, 255, 255, 0.5);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .growth-label {
      font-size: 13px;
      color: rgba(255, 255, 255, 0.85);
      text-transform: uppercase;
      letter-spacing: 1.2px;
      font-weight: 700;
      transition: all 0.4s ease;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    }

    .growth-stage.active .growth-icon {
      color: rgba(255, 255, 255, 0.9);
    }

    /* Connection lines between stages */
    .growth-stage:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 20px;
      right: -20px;
      width: 20px;
      height: 2px;
      background: rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .growth-stage.active:not(:last-child)::after {
      background: #4caf50;
      box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
    }

    .text-block {
      width: 45%;
      padding: 5rem 4rem;
      background: #133a1cf2;
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      position: relative;
      z-index: 5;
    }

    .decorative-leaf {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #4caf50, #b4f8b8);
      border-radius: 0 100% 0 100%;
      margin-bottom: 2rem;
      position: relative;
      animation: leafFloat 6s ease-in-out infinite;
    }

    .decorative-leaf::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 2px;
      height: 30px;
      background: hsl(123, 43%, 39%);
      transform: translate(-50%, -50%) rotate(45deg);
    }

    @keyframes leafFloat {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-10px) rotate(5deg); }
    }

    .text-block h1 {
      font-family: 'Playfair Display', serif;
      font-size: 3.2rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #b4ddb7;
      line-height: 1.2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .text-block h1::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 0;
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, #b5d3b6, #fdfdfd);
      border-radius: 2px;
    }

    .text-block p {
      font-size: 1.25rem;
      color: #96c298;
      line-height: 1.8;
      max-width: 500px;
      margin-bottom: 2rem;
      font-weight: 400;
    }

    .nature-accent {
      color: #91ff96;
      font-weight: 500;
      position: relative;
    }

    .nature-accent::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #81c784, transparent);
    }

    .location-badge {
      display: inline-flex;
      align-items: center;
      background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
      border: 2px solid #c8e6c9;
      border-radius: 25px;
      padding: 12px 24px;
      font-size: 0.9rem;
      font-weight: 500;
      color: #1b5e20;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
    }

    .location-badge::before {
      content: '🌿';
      margin-right: 8px;
      font-size: 1.1rem;
    }

    .location-badge::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s ease;
    }

    .location-badge:hover::after {
      left: 100%;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .text-block h1 {
        font-size: 2.8rem;
      }
      
      .text-block p {
        font-size: 1.1rem;
      }
      
      .image-slider {
        height: 70vh;
      }
    }

    @media (max-width: 768px) {
      .hero-section {
        flex-direction: column;
      }

      .image-slider,
      .text-block {
        width: 100%;
      }

      .image-slider {
        height: 50vh;
        border-radius: 0;
      }

      .text-block {
        padding: 3rem 2rem;
        text-align: center;
        align-items: center;
      }

      .text-block h1 {
        font-size: 2.5rem;
        text-align: center;
      }

      .text-block h1::after {
        left: 50%;
        transform: translateX(-50%);
      }

      .decorative-leaf {
        align-self: center;
        width: 50px;
        height: 50px;
      }
      
      .location-badge {
        padding: 10px 20px;
        font-size: 0.85rem;
      }
      
      .elegant-controls {
        padding: 8px 16px;
        bottom: 20px;
      }
      
    .elegant-dot {
      width: 12px;
      height: 12px;
      touch-action: manipulation;
    }
    }

    @media (max-width: 480px) {
      .text-block {
        padding: 2rem 1.5rem;
      }

      .text-block h1 {
        font-size: 2rem;
      }

      .text-block p {
        font-size: 1rem;
        line-height: 1.6;
      }
      
      ul {
        font-size: 0.95rem;
      }
      
      .image-slider {
        height: 40vh;
      }
      
      .decorative-leaf {
        width: 40px;
        height: 40px;
        margin-bottom: 1.5rem;
      }
    }
    
    @media (max-width: 360px) {
      .text-block h1 {
        font-size: 1.8rem;
      }
      
      .text-block p {
        font-size: 0.9rem;
      }
      
      .location-badge {
        padding: 8px 16px;
        font-size: 0.8rem;
      }
      
      .image-slider {
        height: 35vh;
      }
    }

    /* Smooth animations */
    .text-block * {
      animation: fadeInUp 1s ease-out forwards;
      opacity: 0;
      transform: translateY(30px);
    }

    .text-block h1 { animation-delay: 0.2s; }
    .text-block p { animation-delay: 0.4s; }
    .location-badge { animation-delay: 0.6s; }

    @keyframes fadeInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body>

<div class="hero-section">
  <div class="image-slider" id="slider">
    <img src="https://delwest.local/storage/home/<USER>" class="active" alt="Delwest Bungalow Exterior">
    <img src="https://delwest.local/storage/about/abt1-1.jpg" alt="Delwest Bungalow Interior">

    <!-- Floating Agricultural Elements -->
    <div class="floating-elements">
      <div class="floating-leaf"></div>
      <div class="floating-leaf"></div>
      <div class="floating-leaf"></div>
      <div class="floating-leaf"></div>
    </div>

    <!-- Loading Animation -->
    <div class="slider-loading" id="sliderLoading">
      <div class="loading-sprout"></div>
    </div>
    
    <!-- Modern Progress Bar Slider -->
    <!--
    <div class="slider-controls">
      <div class="slider-progress">
        <div class="progress-segment active" onclick="currentSlide(1)" data-label="Crop 1"></div>
        <div class="progress-segment" onclick="currentSlide(2)" data-label="Crop 2"></div>
        <div class="progress-segment" onclick="currentSlide(3)" data-label="Crop 3"></div>
        <div class="progress-segment" onclick="currentSlide(4)" data-label="Crop 4"></div>
      </div>
    </div>
    -->

    <!-- Alternative: Seed Growth Controls (uncomment to use) -->
    <!--
    <div class="seed-controls">
      <div class="seed-dot active" onclick="currentSlide(1)"></div>
      <div class="seed-dot" onclick="currentSlide(2)"></div>
      <div class="seed-dot" onclick="currentSlide(3)"></div>
      <div class="seed-dot" onclick="currentSlide(4)"></div>
    </div>
    -->

    <!-- Alternative: Leaf Navigation (uncomment to use) -->
    <!--
    <div class="leaf-nav">
      <div class="leaf-control active" onclick="currentSlide(1)"></div>
      <div class="leaf-control" onclick="currentSlide(2)"></div>
      <div class="leaf-control" onclick="currentSlide(3)"></div>
      <div class="leaf-control" onclick="currentSlide(4)"></div>
    </div>
    -->

    <!-- Elegant Dot Navigation for About Us Section -->
    <div class="elegant-controls" style="position: absolute; bottom: 30px; left: 50%; transform: translateX(-50%); display: flex; gap: 12px; z-index: 10; background: rgba(0, 0, 0, 0.4); backdrop-filter: blur(10px); padding: 10px 20px; border-radius: 30px; border: 1px solid rgba(255, 255, 255, 0.2);">
      <div class="elegant-dot active" onclick="currentSlide(1)" style="width: 14px; height: 14px; background: rgba(255, 255, 255, 0.3); border-radius: 50%; cursor: pointer; transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.5);"></div>
      <div class="elegant-dot" onclick="currentSlide(2)" style="width: 14px; height: 14px; background: rgba(255, 255, 255, 0.3); border-radius: 50%; cursor: pointer; transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.5);"></div>
    </div>
  </div>
  
  <div class="text-block">
    <div class="decorative-leaf"></div>
    <h1>Your Serene Escape in the Heart of Sri Lanka</h1>
    <p>
      Delwest Bungalow offers a tranquil highland retreat in Hakgala, Nuwara Eliya, where guests can reconnect with nature amid mist-covered hills and sweeping mountain views. Perfect for families, couples, friends, or solo travelers, this sanctuary combines peaceful relaxation with authentic experiences, including access to a working vegetable farm and proximity to Nuwara Eliya's most beloved attractions.
    </p>
    <ul style="list-style-type: none; margin-bottom: 2rem; color: #96c298; font-size: 1.1rem; line-height: 1.6;">
      <li style="position: relative; padding-left: 1.5rem; margin-bottom: 0.5rem;">Mountain Sanctuary & Views
        <span style="position: absolute; left: 0; color: #91ff96;">•</span>
      </li>
      <li style="position: relative; padding-left: 1.5rem; margin-bottom: 0.5rem;">Working Vegetable Farm Experience
        <span style="position: absolute; left: 0; color: #91ff96;">•</span>
      </li>
      <li style="position: relative; padding-left: 1.5rem;">Prime Location & Attractions
        <span style="position: absolute; left: 0; color: #91ff96;">•</span>
      </li>
    </ul>
    <div class="location-badge">
      Delwest Bungalow and Farm
    </div>
  </div>
</div>

  <script>
  const images = document.querySelectorAll('.image-slider img');
  const progressSegments = document.querySelectorAll('.progress-segment');
  const seedDots = document.querySelectorAll('.seed-dot');
  const leafControls = document.querySelectorAll('.leaf-control');
  const growthStages = document.querySelectorAll('.growth-stage');
  const elegantDots = document.querySelectorAll('.elegant-dot');
  let current = 0;
  let slideInterval;

  function showImage(index) {
    // Remove active class from current image and all controls
    images[current].classList.remove('active');

    // Remove active from all control types
    progressSegments[current]?.classList.remove('active');
    seedDots[current]?.classList.remove('active');
    leafControls[current]?.classList.remove('active');
    growthStages[current]?.classList.remove('active');
    elegantDots[current]?.classList.remove('active');

    // Update current index
    current = index;

    // Add active class to new image and controls
    images[current].classList.add('active');
    progressSegments[current]?.classList.add('active');
    seedDots[current]?.classList.add('active');
    leafControls[current]?.classList.add('active');
    growthStages[current]?.classList.add('active');
    elegantDots[current]?.classList.add('active');
  }

  function showNextImage() {
    const next = (current + 1) % images.length;
    showImage(next);
  }

  function currentSlide(index) {
    showImage(index - 1);
    resetInterval();
  }

  function resetInterval() {
    clearInterval(slideInterval);
    slideInterval = setInterval(showNextImage, 5000);
  }

  // Initialize the slideshow
  function initSlideshow() {
    // Start automatic slideshow
    slideInterval = setInterval(showNextImage, 5000);

    // Pause on hover
    const slider = document.getElementById('slider');
    slider.addEventListener('mouseenter', () => clearInterval(slideInterval));
    slider.addEventListener('mouseleave', resetInterval);

    // Add hover effects for progress segments
    progressSegments.forEach((segment, index) => {
      segment.addEventListener('mouseenter', () => {
        segment.style.background = 'rgba(255, 255, 255, 0.6)';
      });

      segment.addEventListener('mouseleave', () => {
        if (!segment.classList.contains('active')) {
          segment.style.background = 'rgba(255, 255, 255, 0.3)';
        }
      });
    });

    // Add tooltip functionality
    progressSegments.forEach((segment, index) => {
      const tooltip = document.createElement('div');
      tooltip.className = 'slider-tooltip';
      tooltip.textContent = `Image ${index + 1}`;
      tooltip.style.cssText = `
        position: absolute;
        bottom: 120%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
        white-space: nowrap;
      `;

      segment.appendChild(tooltip);

      segment.addEventListener('mouseenter', () => {
        tooltip.style.opacity = '1';
      });

      segment.addEventListener('mouseleave', () => {
        tooltip.style.opacity = '0';
      });
    });
  }

  // Start slideshow when page loads
  document.addEventListener('DOMContentLoaded', initSlideshow);

  // Add smooth scroll behavior for page navigation
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowLeft') {
      const prev = current === 0 ? images.length - 1 : current - 1;
      showImage(prev);
      resetInterval();
    } else if (e.key === 'ArrowRight') {
      showNextImage();
      resetInterval();
    }
  });
</script>

</body>
</html>
