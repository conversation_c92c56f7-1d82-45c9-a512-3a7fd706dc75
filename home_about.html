<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Hill Country Sanctuary | Sri Lanka Hospitality</title>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500&display=swap" rel="stylesheet">
  
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: linear-gradient(135deg, #1a4d1a 0%, #0d2818 100%);
      color: #2e7d32;
      overflow-x: hidden;
      line-height: 1.6;
    }

    .hero-section {
      display: flex;
      width: 100%;
      min-height: 100vh;
      align-items: center;
      justify-content: space-between;
      position: relative;
      overflow: hidden;
      gap: 0;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 400px;
      height: 400px;
      background: radial-gradient(circle, rgba(50, 205, 50, 0.15) 0%, transparent 70%);
      border-radius: 50%;
      z-index: 0;
      animation: pulse 8s infinite;
    }

    .hero-section::after {
      content: '';
      position: absolute;
      bottom: -200px;
      left: -200px;
      width: 500px;
      height: 500px;
      background: radial-gradient(circle, rgba(144, 238, 144, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      z-index: 0;
      animation: pulse 12s infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 0.8; }
      50% { transform: scale(1.05); opacity: 1; }
    }

    .image-slider {
      width: 60%;
      height: 100vh;
      position: relative;
      border-radius: 0 30px 30px 0;
      overflow: hidden;
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      z-index: 1;
    }

    .image-slider::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(56, 142, 60, 0.15) 0%, transparent 50%);
      z-index: 2;
      pointer-events: none;
    }

    /* Automatic Slider - No Controls Needed */

    /* Enhanced Image Transitions */
    .image-slider img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition: all 3s ease-in-out;
      transform: scale(1.05);
      filter: brightness(0.9) contrast(1.1) saturate(1.2);
    }

    .image-slider img.active {
      opacity: 1;
      z-index: 1;
      transform: scale(1);
      filter: brightness(1) contrast(1) saturate(1);
    }

    /* Enhanced Floating Elements Animation */
    .floating-elements {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 3;
    }

    .floating-leaf {
      position: absolute;
      width: 25px;
      height: 25px;
      background: linear-gradient(135deg, #66bb6a, #a5d6a7);
      border-radius: 0 100% 0 100%;
      opacity: 0.4;
      animation: floatLeaf 18s infinite linear;
      box-shadow: 0 4px 10px rgba(102, 187, 106, 0.3);
    }

    .floating-leaf:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
    .floating-leaf:nth-child(2) { top: 30%; left: 80%; animation-delay: 4s; }
    .floating-leaf:nth-child(3) { top: 60%; left: 20%; animation-delay: 8s; }
    .floating-leaf:nth-child(4) { top: 80%; left: 70%; animation-delay: 12s; }

    @keyframes floatLeaf {
      0% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0;
      }
      10% {
        opacity: 0.4;
      }
      50% {
        transform: translateY(-25px) rotate(10deg) scale(1.1);
        opacity: 0.6;
      }
      90% {
        opacity: 0.4;
      }
      100% {
        transform: translateY(-0px) rotate(0deg) scale(1);
        opacity: 0;
      }
    }

    /* Enhanced Slider Loading Animation */
    .slider-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 5;
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .slider-loading.show {
      opacity: 1;
    }

    .loading-sprout {
      width: 45px;
      height: 45px;
      position: relative;
    }

    .loading-sprout::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 5px;
      height: 25px;
      background: #66bb6a;
      transform: translateX(-50%);
      animation: growStem 1.2s ease-in-out infinite;
    }

    .loading-sprout::after {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      width: 20px;
      height: 20px;
      background: linear-gradient(135deg, #66bb6a, #a5d6a7);
      border-radius: 0 100% 0 100%;
      transform: translateX(-50%);
      animation: growLeaf 1.2s ease-in-out infinite 0.6s;
    }

    @keyframes growStem {
      0%, 100% { height: 25px; }
      50% { height: 35px; }
    }

    @keyframes growLeaf {
      0%, 100% { transform: translateX(-50%) scale(1); }
      50% { transform: translateX(-50%) scale(1.3); }
    }

    /* Clean automatic slider - no controls */

    .text-block {
      width: 40%;
      padding: 4rem 3rem;
      background: rgba(19, 58, 28, 0.95);
      backdrop-filter: blur(15px);
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      position: relative;
      z-index: 5;
      border-left: 4px solid rgba(76, 175, 80, 0.3);
    }

    /* Falling Nature Icons */
    .nature-rain {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 9999;
      overflow: hidden;
    }

    .falling-icon {
      position: absolute;
      font-size: 20px;
      opacity: 0.6;
      animation: fall linear infinite;
      user-select: none;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }

    .falling-icon:nth-child(1) { left: 10%; animation-duration: 8s; animation-delay: 0s; }
    .falling-icon:nth-child(2) { left: 20%; animation-duration: 12s; animation-delay: 2s; }
    .falling-icon:nth-child(3) { left: 30%; animation-duration: 10s; animation-delay: 4s; }
    .falling-icon:nth-child(4) { left: 40%; animation-duration: 14s; animation-delay: 1s; }
    .falling-icon:nth-child(5) { left: 50%; animation-duration: 9s; animation-delay: 3s; }
    .falling-icon:nth-child(6) { left: 60%; animation-duration: 11s; animation-delay: 5s; }
    .falling-icon:nth-child(7) { left: 70%; animation-duration: 13s; animation-delay: 0.5s; }
    .falling-icon:nth-child(8) { left: 80%; animation-duration: 15s; animation-delay: 2.5s; }
    .falling-icon:nth-child(9) { left: 90%; animation-duration: 7s; animation-delay: 4.5s; }
    .falling-icon:nth-child(10) { left: 15%; animation-duration: 16s; animation-delay: 6s; }

    @keyframes fall {
      0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 0.6;
      }
      90% {
        opacity: 0.6;
      }
      100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
      }
    }

    .text-block h1 {
      font-family: 'Playfair Display', serif;
      font-size: 3rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #c8e6c9;
      line-height: 1.3;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      position: relative;
    }

    .text-block h1::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 0;
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, #b5d3b6, #fdfdfd);
      border-radius: 2px;
    }

    .text-block p {
      font-size: 1.2rem;
      color: #a5d6a7;
      line-height: 1.8;
      max-width: 100%;
      margin-bottom: 2rem;
      font-weight: 400;
      text-align: justify;
    }

    .nature-accent {
      color: #91ff96;
      font-weight: 500;
      position: relative;
    }

    .nature-accent::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #81c784, transparent);
    }

    .location-badge {
      display: inline-flex;
      align-items: center;
      background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
      border: 2px solid #c8e6c9;
      border-radius: 25px;
      padding: 12px 24px;
      font-size: 0.9rem;
      font-weight: 500;
      color: #1b5e20;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
    }

    .location-badge::before {
      content: '🌿';
      margin-right: 8px;
      font-size: 1.1rem;
    }

    .location-badge::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s ease;
    }

    .location-badge:hover::after {
      left: 100%;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .text-block {
        width: 45%;
        padding: 3.5rem 2.5rem;
      }

      .image-slider {
        width: 55%;
      }

      .text-block h1 {
        font-size: 2.8rem;
      }

      .text-block p {
        font-size: 1.1rem;
      }
    }

    @media (max-width: 1024px) {
      .hero-section {
        flex-direction: column;
        min-height: auto;
      }

      .image-slider,
      .text-block {
        width: 100%;
      }

      .image-slider {
        height: 60vh;
        border-radius: 0 0 30px 30px;
        order: 1;
      }

      .text-block {
        padding: 3rem 2rem;
        text-align: left;
        align-items: flex-start;
        order: 2;
        border-left: none;
        border-top: 4px solid rgba(76, 175, 80, 0.3);
      }

      .text-block h1 {
        font-size: 2.5rem;
      }


    }

    @media (max-width: 768px) {
      .image-slider {
        height: 50vh;
        border-radius: 0;
      }

      .text-block {
        padding: 2.5rem 1.5rem;
        text-align: center;
        align-items: center;
      }

      .text-block h1 {
        font-size: 2.2rem;
        text-align: center;
      }

      .text-block h1::after {
        left: 50%;
        transform: translateX(-50%);
      }

      .text-block p {
        text-align: center;
      }



      .location-badge {
        padding: 10px 20px;
        font-size: 0.85rem;
      }
    }

    @media (max-width: 480px) {
      .text-block {
        padding: 2rem 1.2rem;
      }

      .text-block h1 {
        font-size: 1.9rem;
      }

      .text-block p {
        font-size: 1rem;
        line-height: 1.7;
      }

      ul {
        font-size: 0.95rem;
      }

      .image-slider {
        height: 45vh;
      }



      .location-badge {
        padding: 8px 16px;
        font-size: 0.8rem;
      }
    }

    @media (max-width: 360px) {
      .text-block {
        padding: 1.5rem 1rem;
      }

      .text-block h1 {
        font-size: 1.7rem;
      }

      .text-block p {
        font-size: 0.95rem;
      }

      .image-slider {
        height: 40vh;
      }
    }

    /* Smooth animations */
    .text-block * {
      animation: fadeInUp 1s ease-out forwards;
      opacity: 0;
      transform: translateY(30px);
    }

    .text-block h1 { animation-delay: 0.2s; }
    .text-block p { animation-delay: 0.4s; }
    .location-badge { animation-delay: 0.6s; }

    @keyframes fadeInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body>

<!-- Falling Nature Icons -->
<div class="nature-rain">
  <div class="falling-icon">🍃</div>
  <div class="falling-icon">🌿</div>
  <div class="falling-icon">🍀</div>
  <div class="falling-icon">🌱</div>
  <div class="falling-icon">🍃</div>
  <div class="falling-icon">🌾</div>
  <div class="falling-icon">🌿</div>
  <div class="falling-icon">🍀</div>
  <div class="falling-icon">🌱</div>
  <div class="falling-icon">🍃</div>
</div>

<div class="hero-section">
  <div class="image-slider" id="slider">
    <img src="https://delwest.local/storage/home/<USER>" class="active" alt="Delwest Bungalow Exterior" loading="lazy">
    <img src="https://delwest.local/storage/about/abt1-1.jpg" alt="Delwest Bungalow Interior" loading="lazy">

    <!-- Floating Agricultural Elements -->
    <div class="floating-elements">
      <div class="floating-leaf"></div>
      <div class="floating-leaf"></div>
      <div class="floating-leaf"></div>
      <div class="floating-leaf"></div>
    </div>

    <!-- Automatic slider - no controls needed -->
  </div>
  
  <div class="text-block">
    <h1>Your Serene Escape in the Heart of Sri Lanka</h1>
    <p>
      Delwest Bungalow offers a tranquil highland retreat in Hakgala, Nuwara Eliya, where guests can reconnect with nature amid mist-covered hills and sweeping mountain views. Perfect for families, couples, friends, or solo travelers, this sanctuary combines peaceful relaxation with authentic experiences, including access to a working vegetable farm and proximity to Nuwara Eliya's most beloved attractions.
    </p>
    <ul style="list-style-type: none; margin-bottom: 2rem; color: #96c298; font-size: 1.1rem; line-height: 1.6;">
      <li style="position: relative; padding-left: 1.5rem; margin-bottom: 0.5rem;">Mountain Sanctuary & Views
        <span style="position: absolute; left: 0; color: #91ff96;">•</span>
      </li>
      <li style="position: relative; padding-left: 1.5rem; margin-bottom: 0.5rem;">Working Vegetable Farm Experience
        <span style="position: absolute; left: 0; color: #91ff96;">•</span>
      </li>
      <li style="position: relative; padding-left: 1.5rem;">Prime Location & Attractions
        <span style="position: absolute; left: 0; color: #91ff96;">•</span>
      </li>
    </ul>
  </div>
</div>

  <script>
  const images = document.querySelectorAll('.image-slider img');
  let current = 0;
  let slideInterval;

  function showImage(index) {
    // Remove active class from current image
    images[current].classList.remove('active');

    // Update current index
    current = index;

    // Add active class to new image
    images[current].classList.add('active');
  }

  function showNextImage() {
    const next = (current + 1) % images.length;
    showImage(next);
  }

  function resetInterval() {
    clearInterval(slideInterval);
    slideInterval = setInterval(showNextImage, 6000); // 6 seconds for better viewing
  }

  // Initialize the slideshow
  function initSlideshow() {
    // Start automatic slideshow
    slideInterval = setInterval(showNextImage, 6000);

    // Pause on hover for better UX
    const slider = document.getElementById('slider');
    slider.addEventListener('mouseenter', () => clearInterval(slideInterval));
    slider.addEventListener('mouseleave', resetInterval);
  }

  // Start slideshow when page loads
  document.addEventListener('DOMContentLoaded', initSlideshow);

  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Dynamic Nature Icons
  function createFallingIcon() {
    const icons = ['🍃', '🌿', '🍀', '🌱', '🌾', '🌳', '🌲', '🌴', '🌵', '🌻'];
    const icon = document.createElement('div');
    icon.className = 'falling-icon';
    icon.textContent = icons[Math.floor(Math.random() * icons.length)];

    // Random position and timing
    icon.style.left = Math.random() * 100 + '%';
    icon.style.animationDuration = (Math.random() * 10 + 8) + 's';
    icon.style.animationDelay = Math.random() * 2 + 's';
    icon.style.fontSize = (Math.random() * 10 + 15) + 'px';
    icon.style.opacity = Math.random() * 0.4 + 0.3;

    document.querySelector('.nature-rain').appendChild(icon);

    // Remove icon after animation
    setTimeout(() => {
      if (icon.parentNode) {
        icon.parentNode.removeChild(icon);
      }
    }, 20000);
  }

  // Create falling icons periodically
  setInterval(createFallingIcon, 3000);

  // Create initial icons
  for (let i = 0; i < 5; i++) {
    setTimeout(createFallingIcon, i * 1000);
  }

  // Optional: Add touch/swipe support for mobile
  let startX = 0;
  let endX = 0;

  const slider = document.getElementById('slider');

  slider.addEventListener('touchstart', (e) => {
    startX = e.touches[0].clientX;
  });

  slider.addEventListener('touchend', (e) => {
    endX = e.changedTouches[0].clientX;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    const diff = startX - endX;

    if (Math.abs(diff) > swipeThreshold) {
      if (diff > 0) {
        // Swipe left - next image
        showNextImage();
      } else {
        // Swipe right - previous image
        const prev = current === 0 ? images.length - 1 : current - 1;
        showImage(prev);
      }
      resetInterval();
    }
  }
</script>

</body>
</html>
