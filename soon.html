<html lang="en"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spirelab Solutions Ltd</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        * { font-family: 'Inter', sans-serif; }
        .fade-in { opacity: 0; transform: translateY(20px); animation: fadeInUp 0.8s ease-out forwards; }
        .fade-in-delay-1 { animation-delay: 0.2s; }
        .fade-in-delay-2 { animation-delay: 0.4s; }
        .fade-in-delay-3 { animation-delay: 0.6s; }
        .fade-in-delay-4 { animation-delay: 0.8s; }
        @keyframes fadeInUp {
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
  <style>
      /* Custom font utilities */
      .font-jakarta { font-family: 'Plus Jakarta Sans', sans-serif !important; }
    </style>
</head>
<body class="bg-black text-white overflow-x-hidden">
    <!-- Three.js Canvas Background -->
    <canvas id="shader-canvas" class="fixed inset-0 w-full h-full -z-10" width="1326" height="1034" style="width: 1326px; height: 1034px;"></canvas>
    
    

    <!-- Hero Section -->
    <section class="relative z-10 min-h-screen flex pr-6 pl-6 items-center justify-center">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="md:text-7xl fade-in-delay-1 fade-in text-5xl font-bold tracking-tighter font-jakarta mb-6">
                Great things take time
                <span class="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400">
                     we're crafting something special.
                </span>
            </h1>
        
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center fade-in-delay-3 fade-in">
                <button class="border border-gray-600 text-white px-8 py-4 rounded-lg font-medium hover:border-gray-400 transition-colors duration-200 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="code" class="lucide lucide-code w-4 h-4"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg>
                    Conact Us
                </button>
            </div>
        </div>
    </section>

   

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Three.js shader setup
        const canvas = document.getElementById('shader-canvas');
        const scene = new THREE.Scene();
        const camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
        const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });

        const vertexShader = `
            void main() {
                gl_Position = vec4(position, 1.0);
            }
        `;

        const fragmentShader = `
            uniform vec2 iResolution;
            uniform float iTime;

            float rayStrength(vec2 raySource, vec2 rayRefDirection, vec2 coord, float seedA, float seedB, float speed) {
                vec2 sourceToCoord = coord - raySource;
                float cosAngle = dot(normalize(sourceToCoord), rayRefDirection);
                
                return clamp(
                    (0.45 + 0.15 * sin(cosAngle * seedA + iTime * speed)) +
                    (0.3 + 0.2 * cos(-cosAngle * seedB + iTime * speed)),
                    0.0, 1.0) *
                    clamp((iResolution.x - length(sourceToCoord)) / iResolution.x, 0.5, 1.0);
            }

            void main() {
                vec2 fragCoord = gl_FragCoord.xy;
                vec2 uv = fragCoord.xy / iResolution.xy;
                uv.y = 1.0 - uv.y;
                vec2 coord = vec2(fragCoord.x, iResolution.y - fragCoord.y);
                
                vec2 rayPos1 = vec2(iResolution.x * 0.7, iResolution.y * -0.4);
                vec2 rayRefDir1 = normalize(vec2(1.0, -0.116));
                float raySeedA1 = 36.2214;
                float raySeedB1 = 21.11349;
                float raySpeed1 = 1.5;
                
                vec2 rayPos2 = vec2(iResolution.x * 0.8, iResolution.y * -0.6);
                vec2 rayRefDir2 = normalize(vec2(1.0, 0.241));
                float raySeedA2 = 22.39910;
                float raySeedB2 = 18.0234;
                float raySpeed2 = 1.1;
                
                vec4 rays1 = vec4(1.0, 1.0, 1.0, 1.0) * rayStrength(rayPos1, rayRefDir1, coord, raySeedA1, raySeedB1, raySpeed1);
                vec4 rays2 = vec4(1.0, 1.0, 1.0, 1.0) * rayStrength(rayPos2, rayRefDir2, coord, raySeedA2, raySeedB2, raySpeed2);
                
                vec4 fragColor = rays1 * 0.5 + rays2 * 0.4;
                
                float brightness = 1.0 - (coord.y / iResolution.y);
                fragColor.x *= 0.1 + (brightness * 0.8);
                fragColor.y *= 0.3 + (brightness * 0.6);
                fragColor.z *= 0.5 + (brightness * 0.5);
                
                gl_FragColor = fragColor * 0.3; // Reduce intensity for background
            }
        `;

        const material = new THREE.ShaderMaterial({
            vertexShader: vertexShader,
            fragmentShader: fragmentShader,
            uniforms: {
                iTime: { value: 0 },
                iResolution: { value: new THREE.Vector2() }
            }
        });

        const geometry = new THREE.PlaneGeometry(2, 2);
        const mesh = new THREE.Mesh(geometry, material);
        scene.add(mesh);

        function resize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            renderer.setSize(width, height);
            material.uniforms.iResolution.value.set(width, height);
        }

        function animate() {
            requestAnimationFrame(animate);
            material.uniforms.iTime.value = performance.now() / 1000;
            renderer.render(scene, camera);
        }

        resize();
        window.addEventListener('resize', resize);
        animate();
    </script>

</body></html>